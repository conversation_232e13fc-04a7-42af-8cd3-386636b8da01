# Vercel Deployment Setup Guide

## 🚨 Black Screen Fix

If your Vercel deployment shows a black screen, follow these steps:

### Step 1: Set Environment Variables in Vercel

1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Select your `datawise-website` project
3. Go to **Settings** → **Environment Variables**
4. Add these variables:

```
Name: VITE_PUBLIC_POSTHOG_KEY
Value: phc_Ail5GRzym2wTFGQd6eOjcO5oUeb5UOC4c7ggE7ruCQM
Environment: Production, Preview, Development

Name: VITE_PUBLIC_POSTHOG_HOST  
Value: https://us.i.posthog.com
Environment: Production, Preview, Development

Name: VITE_ENABLE_DEV_ANALYTICS
Value: false
Environment: Production, Preview
```

### Step 2: Redeploy

After setting the environment variables:

1. Go to **Deployments** tab
2. Click the **...** menu on the latest deployment
3. Select **Redeploy**
4. Wait for deployment to complete

### Step 3: Verify Deployment

1. Visit your deployed site
2. Add `?debug=true` to the URL (e.g., `https://yoursite.vercel.app?debug=true`)
3. You should see a green health check indicator in the top-right corner
4. If red, check the error messages for specific issues

### Common Issues & Solutions

#### Issue: Still showing black screen
**Solution:** 
- Check browser console for JavaScript errors
- Verify all environment variables are set correctly
- Try a hard refresh (Ctrl+F5 or Cmd+Shift+R)

#### Issue: CSS not loading
**Solution:**
- Check if assets are being served correctly
- Verify build completed successfully
- Check for any CSP (Content Security Policy) issues

#### Issue: Analytics errors
**Solution:**
- Verify PostHog keys are correct
- Check if PostHog service is accessible
- Analytics failures should not break the app (they're designed to fail silently)

### Environment Variables Explained

- `VITE_PUBLIC_POSTHOG_KEY`: Your PostHog project API key for analytics
- `VITE_PUBLIC_POSTHOG_HOST`: PostHog API endpoint (US region)
- `VITE_ENABLE_DEV_ANALYTICS`: Controls analytics in development (set to false for production)

### Debugging Commands

If you need to debug locally:

```bash
# Build and preview locally
npm run build
npm run preview

# Check for build errors
npm run build 2>&1 | grep -i error

# Test with production environment variables
VITE_PUBLIC_POSTHOG_KEY=your_key npm run build
```

### Support

If you're still experiencing issues:

1. Check the browser console for errors
2. Verify the health check component shows all green checkmarks
3. Contact support with the specific error messages from the health check

---

**Note:** The health check component will automatically appear in development mode or when `?debug=true` is added to the URL.
