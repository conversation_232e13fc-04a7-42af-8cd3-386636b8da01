#!/bin/bash

echo "Testing MCP servers..."

# Track failures
FAILURES=0

# Test Context7
echo -n "Testing Context7: "
if npx -y @upstash/context7-mcp@latest --version 2>/dev/null; then
    echo "✓ OK"
else
    echo "✗ FAILED"
    FAILURES=$((FAILURES + 1))
fi

# Test filesystem
echo -n "Testing filesystem: "
if npx -y @modelcontextprotocol/server-filesystem --version 2>/dev/null; then
    echo "✓ OK"
else
    echo "✗ FAILED"
    FAILURES=$((FAILURES + 1))
fi

# Test puppeteer
echo -n "Testing puppeteer: "
if npx -y @modelcontextprotocol/server-puppeteer --version 2>/dev/null; then
    echo "✓ OK"
else
    echo "✗ FAILED"
    FAILURES=$((FAILURES + 1))
fi

# Test fetch
echo -n "Testing fetch: "
if npx -y @kazuph/mcp-fetch --version 2>/dev/null; then
    echo "✓ OK"
else
    echo "✗ FAILED"
    FAILURES=$((FAILURES + 1))
fi

# Test browser-tools
echo -n "Testing browser-tools: "
if npx -y @agentdeskai/browser-tools-mcp@1.2.1 --version 2>/dev/null; then
    echo "✓ OK"
else
    echo "✗ FAILED"
    FAILURES=$((FAILURES + 1))
fi

echo ""
if [ $FAILURES -gt 0 ]; then
    echo "❌ $FAILURES server(s) failed. They might be causing the connection issues."
    exit 1
else
    echo "✅ All servers passed!"
    exit 0
fi