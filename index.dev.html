<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Preconnect to critical domains -->
    <link rel="preconnect" href="https://www.datawisebets.com" crossorigin>
    <link rel="preconnect" href="https://identity.netlify.com" crossorigin>
    
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Datawise Bets</title>
    <meta name="description" content="Turn sports betting into a reliable side income with Datawise Bets. Our tools and community of profitable bettors help you make smarter wagers backed by data." />
    
    <!-- Critical CSS for the hero section -->
    <style>
      /* Critical font and hero heading styles */
      @font-face {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v13/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2) format('woff2');
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      
      /* Critical hero styles */
      body, html { 
        margin: 0; 
        padding: 0; 
        background-color: #000;
        color: #fff;
        min-height: 100vh;
      }
      #root {
        min-height: 100vh;
        background-color: #000;
      }
      .hero-container { 
        padding: 24px 16px;
        display: flex;
        flex-direction: column;
        margin-top: 80px;
      }
      @media (min-width: 768px) {
        .hero-container {
          flex-direction: row;
          align-items: center;
          margin-top: 140px;
        }
      }
      .text-2xl {
        font-size: 1.5rem;
        line-height: 2rem;
      }
      .font-bold {
        font-weight: 700;
      }
      .leading-tight {
        line-height: 1.25;
      }
      .tracking-tight {
        letter-spacing: -0.025em;
      }
      .heading-gradient {
        background: linear-gradient(90deg, rgba(247,197,72,1) 0%, rgba(255,229,132,0.9) 50%, rgba(247,197,72,1) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-fill-color: transparent;
      }
      @media (min-width: 640px) {
        .sm\:text-3xl {
          font-size: 1.875rem;
          line-height: 2.25rem;
        }
      }
      @media (min-width: 768px) {
        .md\:text-5xl {
          font-size: 3rem;
          line-height: 3.5rem;
        }
      }
      @media (min-width: 1024px) {
        .lg\:text-6xl {
          font-size: 3.75rem;
          line-height: 4rem;
        }
      }
    </style>
    
    <!-- Critical resource preloading for immediate rendering -->
    <!-- Hero image and logo - start loading immediately -->
    <link rel="preload" as="image" href="/lovable-uploads/HeroImage.webp" fetchpriority="high">
    <link rel="preload" as="image" href="/lovable-uploads/DatawiseLogo.webp" fetchpriority="high">
    
    <!-- First few testimonial avatars for smooth hero transitions -->
    <link rel="preload" as="image" href="/lovable-uploads/hunter5287_avatar.webp" fetchpriority="low">
    <link rel="preload" as="image" href="/lovable-uploads/smallville_81_avatar.webp" fetchpriority="low">
    <link rel="preload" as="image" href="/lovable-uploads/jinx133_avatar.webp" fetchpriority="low">
    
    <!-- Font loading with proper strategy -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap"
          media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap"></noscript>
    
    <!-- Favicon - minimal version -->
    <link rel="icon" href="/favicon/favicon.ico" type="image/x-icon" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://datawisebets.com" />
    
    <!-- Meta tags (pushed below critical resources) -->
    <meta name="author" content="Datawise Bets" />
    <meta name="keywords" content="sports betting, data analytics, betting predictions, profitable betting, sports betting tools, betting community" />
    
    <!-- Open Graph / Social Media -->
    <meta property="og:title" content="Stop Guessing, Start Winning: Sports Betting Backed by Data" />
    <meta property="og:description" content="Turn sports betting into a reliable side income with our tools and community of profitable bettors. Start with a 3-day free trial." />
    <meta property="og:image" content="/SiteImage.png" />
    <meta property="og:url" content="https://datawisebets.com" />
    <meta property="og:type" content="website" />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Stop Guessing, Start Winning: Sports Betting Backed by Data" />
    <meta name="twitter:description" content="Turn sports betting into a reliable side income with our tools and community of profitable bettors. Start with a 3-day free trial." />
    <meta name="twitter:image" content="/SiteImage.png" />
    
    <!-- Deferred favicon resources -->
    <link rel="icon" type="image/svg+xml" href="/favicon/favicon.svg" />
    <link rel="icon" type="image/png" sizes="96x96" href="/favicon/favicon-96x96.png" />
    <link rel="apple-touch-icon" href="/favicon/apple-touch-icon.png" />
    <link rel="manifest" href="/favicon/site.webmanifest" />
    
    <!-- Netlify Identity Widget - deferred -->
    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js" defer></script>
    
    <!-- Google Analytics - moved to end of head, async -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-9DJ3SPJRC7"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-9DJ3SPJRC7', { 'send_page_view': false });
      // Defer initial pageview to reduce initial JS load
      window.addEventListener('load', function() {
        gtag('event', 'page_view');
      });
    </script>
    
    <!-- Hyros Tracking Script -->
    <script>
      var head = document.head;
      var script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = "https://t.datawisebets.com/v1/lst/universal-script?ph=f67aec2dc82e06248fe439e3953c3ae649fe3e91d48710a95887ea00eabb7c28&tag=!clicked&ref_url=" + encodeURIComponent(document.URL);
      head.appendChild(script);
    </script>
    
    <!-- PostHog Analytics -->
    <script>
      !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init capture register register_once register_for_session unregister unregister_for_session getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey getNextSurveyStep identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty createPersonProfile opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing debug".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
      posthog.init('phc_Ail5GRzym2wTFGQd6eOjcO5oUeb5UOC4c7ggE7ruCQM',{api_host:'https://us.i.posthog.com', defaults:'2025-05-24'})
    </script>
    
    <!-- Whop checkout now handled by @whop/react package -->
    <link rel="prefetch" href="/checkout.html">
  </head>

  <body>
    <div id="root"></div>
    <!-- Main application script - with type="module" for better performance -->
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Deferred third-party scripts -->
    <script src="https://cdn.gpteng.co/gptengineer.js" defer type="module"></script>
    
    <!-- Netlify Identity Redirect Script - executed after page load -->
    <script>
      window.addEventListener('load', function() {
        if (window.netlifyIdentity) {
          window.netlifyIdentity.on("init", user => {
            if (!user) {
              window.netlifyIdentity.on("login", () => {
                document.location.href = "/admin/";
              });
            }
          });
        }
      });
    </script>
  </body>
</html>
