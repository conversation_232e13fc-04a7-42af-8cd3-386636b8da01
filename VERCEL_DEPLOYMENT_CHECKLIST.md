# Vercel Deployment Checklist

## CRITICAL BLACK SCREEN FIX

The black screen is caused by Vercel serving the source `index.html` (with `/src/main.tsx`) instead of the built `dist/index.html`. We've implemented several fixes:

1. **Build Verification**: After build, we verify dist/index.html is correct
2. **Root index.html Removal**: We rename index.html to index.dev.html after build
3. **.vercelignore**: Excludes source files from deployment
4. **Debug Endpoint**: Visit `/api/debug` to see what files Vercel can access

### To Debug:
1. After deployment, visit: `https://your-domain.vercel.app/api/debug`
2. Check if `distIndex` shows `hasSrcMain: false` and `hasAssets: true`
3. Check browser DevTools Network tab - index.html should NOT have `/src/main.tsx`

## 1. Environment Variables ✅
You've already set these in Vercel:
- `VITE_PUBLIC_POSTHOG_KEY`
- `VITE_PUBLIC_POSTHOG_HOST`

## 2. Build & Development Settings
Check these in Vercel Project Settings → General:

### Framework Preset
- Should be set to: **Vite**
- If it's set to "Other" or something else, change it to Vite

### Build Command
- Should be: `npm run build`
- Or leave blank to use default

### Output Directory
- Should be: `dist`
- This is where Vite outputs the built files

### Install Command
- Should be: `npm install` or leave blank for default

## 3. Node Version
In Settings → General → Node.js Version:
- Should be: **18.x** or **20.x**
- If using an older version, update it

## 4. Root Directory
- Should be: `.` (or leave blank)
- Make sure it's not set to a subdirectory

## 5. Environment Variables Scope
In Settings → Environment Variables:
- Make sure variables are set for: **Production**, **Preview**, and **Development**
- Not just one environment

## 6. Deployment Protection
In Settings → Deployment Protection:
- If enabled, make sure you're accessing with proper authentication
- Try disabling temporarily to test

## 7. Functions Region
- Not critical for a static site, but ensure it's set to a region close to your users

## Common Issues:
1. **Wrong Framework Preset**: Vercel might auto-detect incorrectly
2. **Missing dist in Output Directory**: Vite outputs to `dist`, not `build`
3. **Old Node Version**: Can cause compatibility issues
4. **Environment Variables not in all scopes**: Variables might only be set for one environment
5. **React 19 Dependency Conflicts**: Some packages don't support React 19 yet
   - **vaul@0.9.x**: Update to vaul@1.1.2 or later for React 19 support
   - **Radix UI warnings**: Can be ignored, components work correctly
   - Run `npm install` locally first to catch compatibility issues

## To Debug Further:
1. Check the Vercel Function Logs (even for static sites)
2. Look at the browser console for any errors
3. Check Network tab to see if main JS/CSS files are loading
4. Try accessing: `https://your-domain.vercel.app/_next/static/` to see if assets exist