#!/usr/bin/env node

import { execSync } from 'child_process';
import { existsSync, readdirSync, statSync } from 'fs';
import { join, parse } from 'path';

const BLOG_IMAGES_DIR = './public/images/blog';
const QUALITY = 85; // WebP quality setting (0-100)

// Images to convert (excluding already converted WebP files)
const imagesToConvert = [
  'coin-graph.jpg',
  'luck-vs-skill.jpg',
  'Sportsbook.jpg',
  'sportsbook.png',
  'vegas-sportsbook.jpg'
];

console.log('🖼️  Starting blog image conversion to WebP...\n');

// Check if cwebp is installed
try {
  execSync('cwebp -version', { stdio: 'ignore' });
} catch (error) {
  console.error('❌ Error: cwebp is not installed!');
  console.log('\nTo install cwebp:');
  console.log('  macOS: brew install webp');
  console.log('  Ubuntu/Debian: sudo apt-get install webp');
  console.log('  Windows: Download from https://developers.google.com/speed/webp/download');
  process.exit(1);
}

let converted = 0;
let skipped = 0;
let errors = 0;

imagesToConvert.forEach(filename => {
  const inputPath = join(BLOG_IMAGES_DIR, filename);
  const { name } = parse(filename);
  const outputPath = join(BLOG_IMAGES_DIR, `${name}.webp`);
  
  // Check if input file exists
  if (!existsSync(inputPath)) {
    console.log(`⚠️  Skipping ${filename} - file not found`);
    skipped++;
    return;
  }
  
  // Check if output already exists
  if (existsSync(outputPath)) {
    console.log(`⏭️  Skipping ${filename} - ${name}.webp already exists`);
    skipped++;
    return;
  }
  
  // Get original file size
  const originalSize = statSync(inputPath).size;
  
  try {
    console.log(`🔄 Converting ${filename}...`);
    
    // Convert to WebP with quality setting
    execSync(`cwebp -q ${QUALITY} "${inputPath}" -o "${outputPath}"`, { stdio: 'inherit' });
    
    // Get new file size
    const newSize = statSync(outputPath).size;
    const reduction = ((originalSize - newSize) / originalSize * 100).toFixed(1);
    
    console.log(`✅ Converted ${filename} -> ${name}.webp`);
    console.log(`   Original: ${(originalSize / 1024).toFixed(1)}KB -> WebP: ${(newSize / 1024).toFixed(1)}KB (${reduction}% reduction)\n`);
    
    converted++;
  } catch (error) {
    console.error(`❌ Error converting ${filename}:`, error.message);
    errors++;
  }
});

console.log('\n📊 Conversion Summary:');
console.log(`   ✅ Converted: ${converted} images`);
console.log(`   ⏭️  Skipped: ${skipped} images`);
console.log(`   ❌ Errors: ${errors} images`);

if (converted > 0) {
  console.log('\n💡 Next steps:');
  console.log('   1. Update your code to reference the new .webp files');
  console.log('   2. Consider keeping the original files as fallbacks');
  console.log('   3. Test the images in your application');
  console.log('   4. Once verified, you can optionally remove the original files');
}