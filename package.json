{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"predev": "node scripts/build-blog-content.js && [ -f index.dev.html ] && mv index.dev.html index.html || true", "dev": "vite", "prebuild": "node scripts/build-blog-content.js && [ -f index.dev.html ] && mv index.dev.html index.html || true", "build": "vite build && node scripts/verify-build.js", "build:sourcemap": "vite build --sourcemap && node scripts/verify-build.js", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "@whop/react": "^0.2.0", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.4.7", "glob": "^11.0.1", "gray-matter": "^4.0.3", "input-otp": "^1.2.4", "lucide-react": "^0.479.0", "posthog-js": "^1.232.7", "react": "19.1.0", "react-day-picker": "^9.6.1", "react-dom": "19.1.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^7.3.0", "recharts": "^2.12.7", "rehype-add-classes": "^1.0.0", "rehype-autolink-headings": "^7.1.0", "rehype-sanitize": "^6.0.0", "rehype-slug": "^6.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "remark-rehype": "^11.1.2", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tiny-emitter": "^2.1.0", "vaul": "^1.1.2", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.5.5", "@types/react": "19.1.6", "@types/react-dom": "19.1.6", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdom": "^26.1.0", "lovable-tagger": "^1.1.3", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "terser": "^5.41.0", "typescript": "^5.8.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.0", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.2.2"}}