### We finally have the real runtime failure

`Uncaught ReferenceError: Cannot access 'y' before initialization` means **some module in the bundle is referencing a `let/const` variable named `y` (renamed by <PERSON><PERSON><PERSON>) before the code that defines it has executed.**
That only happens when there is a **circular import** or a self-reference order bug that the minifier couldn’t safely rewrite.

The good news is that it has nothing to do with CSP, Hyros, fonts, or preloads any more. We just need to locate that one module and change a single line.

---

## 1 Re-build with source-maps so we can see the original file/line

```bash
# 1️⃣ Add a one-off sourcemap build
NODE_ENV=production vite build --mode production --sourcemap

# 2️⃣ Preview locally
npx serve dist
#    (Or push a Preview deploy on Vercel and leave SOURCEMAP=true in env)
```

*In devtools make sure “Enable source maps” is ticked (Chrome DevTools → Settings → Sources).*
Reload the page, click the red error. It will now open **the original TypeScript/TSX file** and show the exact line that is executed too early.

> **Send me that file & line** (or just paste the snippet) and we can give you the literal one-line fix.

---

## 2 Most common culprits (check these first while the build runs)

| Pattern that causes the TDZ error                                                                                                                         | Where it exists in your patch                                                                                                 |
| --------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------- |
| Two files importing each other<br>`A.ts` → `export const foo = ...`<br>`B.ts` → `export { foo } from './A'` but also `export const bar` that `A` imports. | **`lib/resourcePreloading.ts`** ↔ **`hooks/useResourcePreloading.ts`** & **`PerformanceMonitor.tsx`** all import one another. |
| Self-reference order inside one file:<br>`export const y = f();`<br>`function f(){ return y + 1 }`                                                        | Minifier tends to rename the variable to `y`.                                                                                 |
| Re-export default + named from same file: `export { default, something } from './module'`                                                                 | Look at any `index.ts` barrels you added.                                                                                     |

Quick test without source-maps: **comment-out the entire `PerformanceMonitor` and `ResourcePreloader` imports in `App.tsx`, rebuild, redeploy.**
If the site renders, the circular reference is inside that new performance-preload code.

---

## 3 If you need a fast emergency hot-fix

1. Comment these three lines in `src/App.tsx` and redeploy:

```tsx
// import ResourcePreloader from "./components/performance/ResourcePreloader";
// import PerformanceMonitor from "./components/performance/PerformanceMonitor";
// <ResourcePreloader ... />
// {process.env.NODE_ENV === 'development' && (<PerformanceMonitor ... />)}
```

2. Clear the Vercel cache and redeploy.

The site will load (without the fancy preloading/monitor overlay).
You can then take your time to trace the circular import locally using the source-mapped build.

---

## 4 Once you know the file, fix is always one of these

* **Break the circular import**: move the shared constant/function into a third file `shared.ts`, import it from both places.
* **Delay the reference**: wrap it in a function so the variable isn’t read at module-initialisation time.
* **Change `export const x = something(y)`** to `let x; export { x }; x = something(y)` when you *must* keep the circle (rare).

---

### Next step

Do the *source-map build*, grab the red-error stack that now points to your TS source, and send that snippet here. We can then give you the exact 3-line change instead of disabling the feature.
