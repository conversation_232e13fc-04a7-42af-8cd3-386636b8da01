
import { useEffect, useReducer, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import TestimonialGrid from "./testimonials/TestimonialGrid";
import TestimonialPagination from "./testimonials/TestimonialPagination";
import { testimonials } from "./testimonials/testimonialData";

const TESTIMONIALS_PER_PAGE = 4;
const ROTATION_INTERVAL = 15000; // Increased to 15s to allow smoother viewing experience
const ANIMATION_DURATION = 1500; // 1500ms animation duration for smoother transitions

// State interface for testimonials component
interface TestimonialsState {
  visibleIndices: number[];
  isAnimating: boolean;
  isMobile: boolean;
}

// Action types for state management
type TestimonialsAction =
  | { type: 'NEXT' }
  | { type: 'PREV' }
  | { type: 'SET_PAGE'; pageIndex: number }
  | { type: 'ANIMATION_END' }
  | { type: 'SET_MOBILE'; isMobile: boolean };

// Initial state
const initialState: TestimonialsState = {
  visibleIndices: [0, 1, 2, 3],
  isAnimating: false,
  isMobile: false,
};

// Helper function to calculate new indices
const calculateIndices = (firstIndex: number): number[] => [
  firstIndex,
  (firstIndex + 1) % testimonials.length,
  (firstIndex + 2) % testimonials.length,
  (firstIndex + 3) % testimonials.length,
];

// Reducer function for centralized state management
function testimonialsReducer(state: TestimonialsState, action: TestimonialsAction): TestimonialsState {
  switch (action.type) {
    case 'NEXT': {
      if (state.isAnimating) return state;
      const firstIndex = (state.visibleIndices[0] + TESTIMONIALS_PER_PAGE) % testimonials.length;
      return {
        ...state,
        visibleIndices: calculateIndices(firstIndex),
        isAnimating: true,
      };
    }
    case 'PREV': {
      if (state.isAnimating) return state;
      const firstIndex = (state.visibleIndices[0] - TESTIMONIALS_PER_PAGE + testimonials.length) % testimonials.length;
      return {
        ...state,
        visibleIndices: calculateIndices(firstIndex),
        isAnimating: true,
      };
    }
    case 'SET_PAGE': {
      if (state.isAnimating) return state;
      const firstIndex = (action.pageIndex * TESTIMONIALS_PER_PAGE) % testimonials.length;
      return {
        ...state,
        visibleIndices: calculateIndices(firstIndex),
        isAnimating: true,
      };
    }
    case 'ANIMATION_END': {
      return {
        ...state,
        isAnimating: false,
      };
    }
    case 'SET_MOBILE': {
      return {
        ...state,
        isMobile: action.isMobile,
      };
    }
    default:
      return state;
  }
}

const TestimonialsSection = () => {
  const [state, dispatch] = useReducer(testimonialsReducer, initialState);
  const autoRotateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const sectionRef = useRef<HTMLDivElement>(null);

  // Check for mobile once on mount and when window resizes
  useEffect(() => {
    const checkIfMobile = () => {
      dispatch({ type: 'SET_MOBILE', isMobile: window.innerWidth < 768 });
    };
    
    // Initial check
    checkIfMobile();
    
    // Add resize listener with debounce
    let timeoutId: number;
    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = window.setTimeout(checkIfMobile, 200);
    };
    
    window.addEventListener('resize', handleResize, { passive: true });
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timeoutId);
    };
  }, []);

  // Memoized handlers using dispatch for centralized state management
  const handleNext = useCallback(() => {
    if (state.isAnimating) return;
    
    dispatch({ type: 'NEXT' });
    setTimeout(() => {
      dispatch({ type: 'ANIMATION_END' });
    }, ANIMATION_DURATION + 200); // Add buffer to ensure animation completes before allowing next animation
  }, [state.isAnimating]);

  const handlePrev = useCallback(() => {
    if (state.isAnimating) return;
    
    dispatch({ type: 'PREV' });
    setTimeout(() => {
      dispatch({ type: 'ANIMATION_END' });
    }, ANIMATION_DURATION + 200); // Add buffer to ensure animation completes
  }, [state.isAnimating]);

  const handlePageClick = useCallback((pageIndex: number) => {
    if (state.isAnimating) return;
    
    dispatch({ type: 'SET_PAGE', pageIndex });
    setTimeout(() => {
      dispatch({ type: 'ANIMATION_END' });
    }, ANIMATION_DURATION + 200); // Add buffer to ensure animation completes
  }, [state.isAnimating]);

  // Auto-rotation effect
  useEffect(() => {
    // Only auto-rotate on mobile
    if (!state.isMobile) return;
    
    const setupAutoRotation = () => {
      // Clear any existing timeout
      if (autoRotateTimeoutRef.current) {
        clearTimeout(autoRotateTimeoutRef.current);
      }
      
      // Set up new timeout if not animating
      if (!state.isAnimating) {
        autoRotateTimeoutRef.current = setTimeout(() => {
          handleNext();
        }, ROTATION_INTERVAL);
      }
    };
    
    // Set up initial rotation
    setupAutoRotation();
    
    // Set up rotation after animation completes
    return () => {
      if (autoRotateTimeoutRef.current) {
        clearTimeout(autoRotateTimeoutRef.current);
      }
    };
  }, [state.isAnimating, state.isMobile, handleNext]);

  return (
    <section id="testimonials" className="container mx-auto px-4 py-12 md:py-20 relative" ref={sectionRef}>
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 left-10 w-64 h-64 bg-gold/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-10 w-64 h-64 bg-gold/5 rounded-full blur-3xl"></div>
      </div>

      <div className="text-center mb-6 md:mb-8 relative z-10">
        <motion.h2 
          className="text-2xl sm:text-3xl md:text-5xl font-bold mb-2 md:mb-3 leading-tight"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <span className="heading-gradient">Success Stories</span>
        </motion.h2>
        <motion.p 
          className="text-xs sm:text-sm md:text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          See how Datawise has helped bettors increase their profits and gain a competitive edge
        </motion.p>
      </div>
      
      <TestimonialGrid 
        visibleIndices={state.visibleIndices}
        testimonials={testimonials}
        isAnimating={state.isAnimating}
        onNext={handleNext}
        onPrev={handlePrev}
      />
      
      {state.isMobile && (
        <TestimonialPagination 
          totalPages={Math.ceil(testimonials.length / TESTIMONIALS_PER_PAGE)}
          visibleIndices={state.visibleIndices}
          onPageClick={handlePageClick}
          isAnimating={state.isAnimating}
          testimonialsPerPage={TESTIMONIALS_PER_PAGE}
        />
      )}
    </section>
  );
};

export default TestimonialsSection;
