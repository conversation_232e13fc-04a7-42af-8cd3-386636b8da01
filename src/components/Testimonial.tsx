import { motion } from "framer-motion";
import { TestimonialData } from "@/types/testimonial";
import { memo } from "react";

interface TestimonialProps {
  data: TestimonialData;
  delay?: number;
}

const Testimonial = ({ data, delay = 0 }: TestimonialProps) => {
  return (
    <div className="h-full py-2">
      <div className="relative">
        {/* Stats badge styled like the Get Started button */}
        {data.stats && (
          <div className="absolute -top-3 -left-1 z-10">
            <div className="bg-black text-gold text-xs font-medium px-3 sm:px-5 py-1 sm:py-1.5 rounded-full 
                          border border-gold/50 shadow-[0_0_10px_rgba(247,197,72,0.15)] 
                          transform group-hover:shadow-[0_0_15px_rgba(247,197,72,0.3)] 
                          transition-all duration-500 group-hover:scale-105">
              {data.stats}
            </div>
          </div>
        )}
        
        <motion.div
          className="glass-card h-full flex flex-col group rounded-lg"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ 
            duration: 0.8, 
            delay,
            ease: [0.25, 0.1, 0.25, 1] // Using cubic-bezier for more natural motion
          }}
          viewport={{ once: true }}
          whileHover={{ 
            boxShadow: "0 12px 24px rgba(255, 215, 0, 0.1)" 
          }}
          style={{
            willChange: "opacity, box-shadow",
            transform: "translateZ(0)", // Force GPU acceleration
            backfaceVisibility: "hidden", // Prevent flickering
            perspective: 1000 // Added for smoother animations
          }}
        >
          {/* Enhanced background highlight effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-gold/5 to-transparent rounded-lg -z-10 opacity-0 group-hover:opacity-100 transition-all duration-700"></div>
          
          {/* Content with hover effect - made more compact on mobile */}
          <p className="text-gray-200 mb-2 px-3 sm:px-5 pt-3 sm:pt-5 flex-grow text-xs sm:text-sm leading-relaxed group-hover:text-white transition-colors duration-500">{data.content}</p>
          
          {/* Result image (if provided) */}
          {data.resultImage && (
            <div className="px-3 sm:px-5 mb-2">
              <div className="rounded-lg overflow-hidden border border-white/10 shadow-lg group-hover:border-gold/20 transition-all duration-500">
                <motion.img 
                  src={data.resultImage} 
                  alt="Performance Results" 
                  className="w-full h-auto rounded-lg"
                  loading="lazy"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.4, ease: [0.25, 0.1, 0.25, 1] }}
                />
              </div>
            </div>
          )}
          
          {/* Avatar and user details with enhanced animations - Bottom */}
          <div className="flex items-center px-3 sm:px-5 pb-3 sm:pb-4 mt-auto border-t border-white/5 pt-2 sm:pt-4 group-hover:border-white/10 transition-colors duration-700">
            <div className="mr-2 sm:mr-3 relative">
              <motion.img
                src={data.avatar}
                alt={data.name}
                className="w-6 h-6 sm:w-8 sm:h-8 rounded-full object-cover border border-gold/20 shadow-md relative z-10"
                loading="lazy"
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.4, ease: [0.25, 0.1, 0.25, 1] }}
              />
            </div>
            <div>
              <h4 className="font-medium text-white text-xs sm:text-sm group-hover:text-gold/90 transition-colors duration-500">{data.name}</h4>
              <p className="text-gray-400 text-[10px] sm:text-xs group-hover:text-gray-300 transition-colors duration-500">{data.position}</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default memo(Testimonial);
