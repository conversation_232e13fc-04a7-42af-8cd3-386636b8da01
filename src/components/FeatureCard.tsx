import { memo } from "react";
import { motion } from "framer-motion";

interface FeatureCardProps {
  title: string;
  icon: React.ReactNode;
  description?: string;
  delay?: number;
}

const FeatureCard = ({ title, icon, description, delay = 0 }: FeatureCardProps) => {
  return (
    <motion.div
      className="glass-card p-3 feature-card h-full relative group overflow-hidden text-center"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      viewport={{ once: true, margin: "-50px" }}
      whileHover={{ 
        y: -4, 
        transition: { duration: 0.2 },
        boxShadow: "0 10px 30px rgba(255, 215, 0, 0.2)"
      }}
      style={{ willChange: "transform, opacity" }}
    >
      {/* Enhanced gradient background effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-gold/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-xl"></div>
      <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl"></div>
      
      {/* Icon container - centered and properly sized */}
      <div className="flex items-center justify-center mb-2">
        <div className="relative inline-block group-hover:scale-110 transition-transform duration-500">
          <div className="absolute inset-0 bg-gold/20 rounded-full blur-md group-hover:blur-lg group-hover:bg-gold/30 transition-all duration-500"></div>
          <div className="text-gold relative z-10 p-1">
            {icon}
          </div>
        </div>
      </div>
      
      {/* Title with improved spacing */}
      <h3 className="text-sm md:text-base font-bold text-white group-hover:text-gold/90 transition-colors duration-300 mb-1">
        {title}
      </h3>
      
      {/* Optional description with better readability - hidden on mobile, visible on sm and up */}
      {description && (
        <p className="hidden sm:block text-xs text-gray-300 mx-auto">
          {description}
        </p>
      )}
      
      {/* Enhanced decorative elements */}
      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-gold/0 via-gold/20 to-gold/0 transform translate-y-0.5 group-hover:via-gold/40 transition-all duration-500"></div>
      
      {/* Refined corner accent */}
      <div className="absolute top-0 right-0 w-8 h-8 bg-gradient-radial from-gold/10 via-gold/5 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
    </motion.div>
  );
};

export default memo(FeatureCard);
