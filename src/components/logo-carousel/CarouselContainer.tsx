
import { ReactNode, memo } from "react";
import { motion, AnimationControls } from "framer-motion";

interface CarouselContainerProps {
  children: ReactNode;
  controls: AnimationControls;
}

// Custom comparison function for CarouselContainer
const arePropsEqual = (prevProps: CarouselContainerProps, nextProps: CarouselContainerProps) => {
  // Since children will always be the same array of logos (just animated),
  // we only need to check if the controls object has changed reference
  return prevProps.controls === nextProps.controls;
};

const CarouselContainer = ({ children, controls }: CarouselContainerProps) => {
  return (
    <div 
      className="container mx-auto overflow-hidden relative"
      style={{ 
        isolation: "isolate",
        backfaceVisibility: "hidden",
        perspective: 1000,
      }}
    >
      <div className="relative w-full flex items-center will-change-transform">
        <motion.div
          className="flex space-x-8 md:space-x-12 lg:space-x-16 items-center infinite-carousel"
          animate={controls}
          style={{ 
            willChange: "transform",
            transform: "translate3d(0,0,0)",
            backfaceVisibility: "hidden",
            WebkitFontSmoothing: "subpixel-antialiased",
            WebkitTransform: "translateZ(0) translateX(0)",
            WebkitBackfaceVisibility: "hidden",
            WebkitPerspective: "1000",
            WebkitTransformStyle: "preserve-3d",
            contain: "layout paint style",
            isolation: "isolate"
          }}
        >
          {children}
        </motion.div>
      </div>
      
      {/* Enhanced gradient fade effect at the edges */}
      <div 
        className="absolute top-0 left-0 h-full w-24 bg-gradient-to-r from-background to-transparent z-10 pointer-events-none"
        style={{ maskImage: "linear-gradient(to right, rgba(0,0,0,1) 0%, transparent 100%)" }}
      ></div>
      <div 
        className="absolute top-0 right-0 h-full w-24 bg-gradient-to-l from-background to-transparent z-10 pointer-events-none"
        style={{ maskImage: "linear-gradient(to left, rgba(0,0,0,1) 0%, transparent 100%)" }}
      ></div>
    </div>
  );
};

// Apply memo with custom comparison function
const MemoizedCarouselContainer = memo(CarouselContainer, arePropsEqual);

// Set displayName for debugging
MemoizedCarouselContainer.displayName = "CarouselContainer";

export default MemoizedCarouselContainer;
