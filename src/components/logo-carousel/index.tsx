
import { memo, useCallback } from "react";
import Logo from "./Logo";
import CarouselContainer from "./CarouselContainer";
import useCarouselAnimation from "./useCarouselAnimation";

interface LogoCarouselProps {
  speed?: number;
}

// Custom comparison function for the main component
const areEqual = (prevProps: LogoCarouselProps, nextProps: LogoCarouselProps) => {
  // Only re-render if the speed prop changes
  return prevProps.speed === nextProps.speed;
};

const LogoCarousel = ({ speed = 30 }: LogoCarouselProps) => {
  const { carouselRef, controls, dupeLogos } = useCarouselAnimation({ speed });
  
  // Memoize the logo rendering function to prevent recreating it on each render
  const renderLogos = useCallback(() => {
    return dupeLogos.map((logo, index) => (
      <Logo key={`${logo.alt}-${index}`} logo={logo} index={index} />
    ));
  }, [dupeLogos]);

  return (
    <section 
      id="logo-carousel" 
      className="w-full py-4 md:py-6 bg-background" 
      ref={carouselRef}
      style={{ 
        contain: "content",
        willChange: "contents",
      }}
    >
      <div className="container mx-auto text-center mb-2 md:mb-4">
        <h3 className="text-xs md:text-sm text-gray-400 uppercase tracking-wide font-medium">Supported Platforms</h3>
      </div>
      
      <CarouselContainer controls={controls}>
        {renderLogos()}
      </CarouselContainer>
    </section>
  );
};

// Apply memo with the custom comparison function
const MemoizedLogoCarousel = memo(LogoCarousel, areEqual);

// Set displayName for debugging
MemoizedLogoCarousel.displayName = "LogoCarousel";

export default MemoizedLogoCarousel;
