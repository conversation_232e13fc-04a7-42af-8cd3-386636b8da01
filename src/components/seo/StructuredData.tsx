import React from 'react';

// Base structured data types
interface BaseStructuredData {
  '@context': string;
  '@type': string;
}

interface Organization extends BaseStructuredData {
  '@type': 'Organization';
  name: string;
  url: string;
  logo: string;
  description: string;
  sameAs: string[];
  contactPoint: {
    '@type': 'ContactPoint';
    contactType: string;
    email?: string;
    url?: string;
  };
}

interface WebSite extends BaseStructuredData {
  '@type': 'WebSite';
  name: string;
  url: string;
  description: string;
  publisher: {
    '@type': 'Organization';
    name: string;
    logo: string;
  };
  potentialAction: {
    '@type': 'SearchAction';
    target: string;
    'query-input': string;
  };
}

interface Article extends BaseStructuredData {
  '@type': 'Article';
  headline: string;
  description: string;
  image: string[];
  datePublished: string;
  dateModified: string;
  author: {
    '@type': 'Person' | 'Organization';
    name: string;
    url?: string;
  };
  publisher: {
    '@type': 'Organization';
    name: string;
    logo: {
      '@type': 'ImageObject';
      url: string;
      width: number;
      height: number;
    };
  };
  mainEntityOfPage: {
    '@type': 'WebPage';
    '@id': string;
  };
  articleSection: string;
  wordCount: number;
  keywords: string[];
}

interface BreadcrumbList extends BaseStructuredData {
  '@type': 'BreadcrumbList';
  itemListElement: Array<{
    '@type': 'ListItem';
    position: number;
    name: string;
    item: string;
  }>;
}

interface FAQPage extends BaseStructuredData {
  '@type': 'FAQPage';
  mainEntity: Array<{
    '@type': 'Question';
    name: string;
    acceptedAnswer: {
      '@type': 'Answer';
      text: string;
    };
  }>;
}

interface SoftwareApplication extends BaseStructuredData {
  '@type': 'SoftwareApplication';
  name: string;
  description: string;
  url: string;
  applicationCategory: string;
  operatingSystem: string;
  offers: {
    '@type': 'Offer';
    price: string;
    priceCurrency: string;
    availability: string;
  };
  aggregateRating?: {
    '@type': 'AggregateRating';
    ratingValue: number;
    reviewCount: number;
    bestRating: number;
    worstRating: number;
  };
}

// Component props interfaces
interface StructuredDataProps {
  data: BaseStructuredData | BaseStructuredData[];
}

interface OrganizationDataProps {
  name?: string;
  url?: string;
  logo?: string;
  description?: string;
  email?: string;
  socialLinks?: string[];
}

interface ArticleDataProps {
  title: string;
  description: string;
  image: string;
  publishedDate: string;
  modifiedDate?: string;
  author: string;
  authorUrl?: string;
  section: string;
  keywords: string[];
  wordCount?: number;
}

interface BreadcrumbDataProps {
  items: Array<{
    name: string;
    url: string;
  }>;
}

interface FAQDataProps {
  faqs: Array<{
    question: string;
    answer: string;
  }>;
}

/**
 * Generic Structured Data Component
 */
export const StructuredData: React.FC<StructuredDataProps> = ({ data }) => {
  const jsonLd = Array.isArray(data) ? data : [data];
  
  return (
    <>
      {jsonLd.map((item, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(item, null, 2),
          }}
        />
      ))}
    </>
  );
};

/**
 * Organization Structured Data
 */
export const OrganizationData: React.FC<OrganizationDataProps> = ({
  name = 'Datawise Bets',
  url = 'https://datawisebets.com',
  logo = 'https://datawisebets.com/favicon/favicon-96x96.png',
  description = 'Turn sports betting into a reliable side income with data-driven insights and tools.',
  email = '<EMAIL>',
  socialLinks = [],
}) => {
  const organizationData: Organization = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name,
    url,
    logo,
    description,
    sameAs: socialLinks,
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'Customer Service',
      email,
      url: `${url}/contact`,
    },
  };

  return <StructuredData data={organizationData} />;
};

/**
 * Website Structured Data
 */
export const WebSiteData: React.FC = () => {
  const websiteData: WebSite = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Datawise Bets',
    url: 'https://datawisebets.com',
    description: 'Data-driven sports betting insights and tools for profitable betting.',
    publisher: {
      '@type': 'Organization',
      name: 'Datawise Bets',
      logo: 'https://datawisebets.com/favicon/favicon-96x96.png',
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://datawisebets.com/search?q={search_term_string}',
      'query-input': 'required name=search_term_string',
    },
  };

  return <StructuredData data={websiteData} />;
};

/**
 * Article Structured Data
 */
export const ArticleData: React.FC<ArticleDataProps> = ({
  title,
  description,
  image,
  publishedDate,
  modifiedDate,
  author,
  authorUrl,
  section,
  keywords,
  wordCount = 0,
}) => {
  const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
  
  const articleData: Article = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description,
    image: [image],
    datePublished: publishedDate,
    dateModified: modifiedDate || publishedDate,
    author: {
      '@type': 'Person',
      name: author,
      url: authorUrl,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Datawise Bets',
      logo: {
        '@type': 'ImageObject',
        url: 'https://datawisebets.com/favicon/favicon-96x96.png',
        width: 96,
        height: 96,
      },
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': currentUrl,
    },
    articleSection: section,
    wordCount,
    keywords,
  };

  return <StructuredData data={articleData} />;
};

/**
 * Breadcrumb Structured Data
 */
export const BreadcrumbData: React.FC<BreadcrumbDataProps> = ({ items }) => {
  const breadcrumbData: BreadcrumbList = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };

  return <StructuredData data={breadcrumbData} />;
};

/**
 * FAQ Structured Data
 */
export const FAQData: React.FC<FAQDataProps> = ({ faqs }) => {
  const faqData: FAQPage = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };

  return <StructuredData data={faqData} />;
};

/**
 * Software Application Structured Data (for betting tools)
 */
export const SoftwareApplicationData: React.FC = () => {
  const appData: SoftwareApplication = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: 'Datawise Bets Betting Simulator',
    description: 'Advanced sports betting simulator with data-driven insights and strategy testing.',
    url: 'https://datawisebets.com/betting-simulator',
    applicationCategory: 'Sports & Recreation',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
  };

  return <StructuredData data={appData} />;
};

/**
 * Combined Structured Data for Homepage
 */
export const HomepageStructuredData: React.FC = () => {
  return (
    <>
      <OrganizationData />
      <WebSiteData />
    </>
  );
};

export default StructuredData;
