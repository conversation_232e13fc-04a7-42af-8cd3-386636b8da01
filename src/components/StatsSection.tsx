
import { motion } from "framer-motion";

const StatsSection = () => {
  const stats = [
    { value: "+$140,742.34", label: "Profit Generated" },
    { value: "+7.21%", label: "Average ROI" },
    { value: "2404-5888", label: "Win-Loss Record" },
    { value: "10,000+", label: "Active Users" },
  ];

  return (
    <section className="py-12 sm:py-16 md:py-24 relative overflow-hidden">
      {/* Gradient transition overlay at the top */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-background to-transparent z-10"></div>
      
      {/* Gradient transition overlay at the bottom */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-background to-transparent z-10"></div>
      
      {/* Subtle background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-gold/5 via-black/95 to-black/95 -z-20"></div>
      
      {/* Refined background pattern */}
      <div className="absolute inset-0 opacity-3" 
           style={{
             backgroundImage: "radial-gradient(circle, rgba(255,215,0,0.2) 1px, transparent 1px)",
             backgroundSize: "30px 30px"
           }}>
      </div>
      
      {/* More subtle decorative elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gold/3 rounded-full blur-[120px] -z-10"></div>
      <div className="absolute bottom-0 left-0 w-72 h-72 bg-gold/3 rounded-full blur-[120px] -z-10"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div 
          className="text-center mb-8 sm:mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 enhanced-gradient-text">2023 Performance Stats</h2>
          <p className="text-sm sm:text-base md:text-lg text-gray-300 max-w-2xl mx-auto">
            Join thousands of bettors who are already profiting with Datawise
          </p>
        </motion.div>
        
        <div className="grid grid-cols-2 gap-4 sm:gap-6 text-center">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className="glass-card p-4 sm:p-6 md:p-8 relative overflow-hidden hover-lift flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ 
                y: -5,
                boxShadow: "0 10px 30px rgba(255, 215, 0, 0.1)"
              }}
            >
              {/* Simplified gradient overlays */}
              <div className="absolute inset-0 bg-gradient-to-br from-gold/3 to-transparent opacity-50 -z-10"></div>
              
              <motion.div 
                className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold enhanced-gradient-text mb-2 sm:mb-3 relative"
                initial={{ scale: 0.9 }}
                whileInView={{ scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 + 0.2 }}
                viewport={{ once: true }}
              >
                {stat.value}
              </motion.div>
              
              <div className="text-xs sm:text-sm md:text-base text-gray-300">{stat.label}</div>
              
              {/* Subtle bottom accent line */}
              <motion.div 
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-gold/20 to-transparent"
                initial={{ scaleX: 0 }}
                whileInView={{ scaleX: 1 }}
                transition={{ duration: 0.8, delay: index * 0.1 + 0.3 }}
                viewport={{ once: true }}
              ></motion.div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
