
import { memo } from "react";
import { TestimonialData } from "@/types/testimonial";
import Testimonial from "@/components/Testimonial";

interface MemoizedTestimonialProps {
  data: TestimonialData;
  delay: number;
}

const MemoizedTestimonial = memo(({ 
  data, 
  delay 
}: MemoizedTestimonialProps) => (
  <Testimonial data={data} delay={delay} />
));

MemoizedTestimonial.displayName = "MemoizedTestimonial";

export default MemoizedTestimonial;
