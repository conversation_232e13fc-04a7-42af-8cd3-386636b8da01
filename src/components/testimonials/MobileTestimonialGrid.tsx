
import { motion, AnimatePresence, PanInfo } from "framer-motion";
import { TestimonialData } from "@/types/testimonial";
import { useRef } from "react";
import MemoizedTestimonial from "./MemoizedTestimonial";

interface MobileTestimonialGridProps {
  visibleIndices: number[];
  testimonials: TestimonialData[];
  isAnimating: boolean;
  direction: 'next' | 'prev';
  initialLoad: boolean;
  onNext?: () => void;
  onPrev?: () => void;
}

const MobileTestimonialGrid = ({ 
  visibleIndices, 
  testimonials, 
  isAnimating,
  direction,
  initialLoad,
  onNext,
  onPrev 
}: MobileTestimonialGridProps) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle drag end with Framer Motion
  const handleDragEnd = (event: any, info: PanInfo) => {
    if (isAnimating) return;
    
    const swipeThreshold = 50;
    const swipeVelocityThreshold = 500;
    
    // Check if the swipe was significant enough (by distance or velocity)
    if (Math.abs(info.offset.x) > swipeThreshold || Math.abs(info.velocity.x) > swipeVelocityThreshold) {
      if (info.offset.x > 0) {
        // Swiped right, go to previous
        onPrev?.();
      } else {
        // Swiped left, go to next
        onNext?.();
      }
    }
  };

  return (
    <motion.div 
      className="relative overflow-hidden pt-8 pb-2 swipeable-container" 
      style={{ 
        height: '720px',
        contain: 'content',
        willChange: 'contents',
        touchAction: 'pan-y'
      }} 
      ref={containerRef}
      drag="x"
      dragConstraints={{ left: 0, right: 0 }}
      dragElastic={0.2}
      onDragEnd={handleDragEnd}
      whileDrag={{ cursor: "grabbing" }}
    >
      <AnimatePresence mode="popLayout" initial={false}>
        <motion.div 
          key={visibleIndices.join('-')}
          className="grid grid-cols-2 gap-3 px-1"
          initial={initialLoad ? false : { opacity: 0, x: direction === 'next' ? 100 : -100 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: direction === 'next' ? -100 : 100 }}
          transition={{ 
            duration: 0.4, 
            ease: [0.32, 0.72, 0, 1],
            delayChildren: 0.05,
            staggerChildren: 0.05
          }}
          style={{ 
            willChange: "transform, opacity",
            pointerEvents: isAnimating ? 'none' : 'auto'
          }}
        >
          {visibleIndices.map((index, i) => (
            <motion.div 
              key={`testimonial-${index}`} 
              className="h-full testimonial-transition"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.5,
                delay: i * 0.05,
                ease: [0.32, 0.72, 0, 1]
              }}
            >
              <MemoizedTestimonial
                data={testimonials[index]} 
                delay={0} 
              />
            </motion.div>
          ))}
        </motion.div>
      </AnimatePresence>
    </motion.div>
  );
};

export default MobileTestimonialGrid;
