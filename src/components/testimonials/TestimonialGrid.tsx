
import { TestimonialData } from "@/types/testimonial";
import { useEffect, useState, memo, useCallback } from "react";
import MobileTestimonialGrid from "./MobileTestimonialGrid";
import DesktopTestimonialGrid from "./DesktopTestimonialGrid";

interface TestimonialGridProps {
  visibleIndices: number[];
  testimonials: TestimonialData[];
  isAnimating: boolean;
  onNext?: () => void;
  onPrev?: () => void;
}

const TestimonialGrid = ({ 
  visibleIndices, 
  testimonials, 
  isAnimating,
  onNext,
  onPrev 
}: TestimonialGridProps) => {
  const [isMobile, setIsMobile] = useState(false);
  const [initialLoad, setInitialLoad] = useState(true);
  const [prevVisibleIndices, setPrevVisibleIndices] = useState<number[]>(visibleIndices);
  const [direction, setDirection] = useState<'next' | 'prev'>('next');

  // Memoized check function to avoid recreation on each render
  const checkIfMobile = useCallback(() => {
    setIsMobile(window.innerWidth < 768);
  }, []);

  // Determine animation direction when indices change
  useEffect(() => {
    if (prevVisibleIndices[0] !== visibleIndices[0]) {
      // Determine if we're going forward or backward
      const prev = prevVisibleIndices[0];
      const current = visibleIndices[0];
      
      // Check if it's a wrap-around case
      if (Math.abs(current - prev) > testimonials.length / 2) {
        // Wrap around case
        setDirection(current < prev ? 'next' : 'prev');
      } else {
        // Normal case
        setDirection(current > prev ? 'next' : 'prev');
      }
      
      setPrevVisibleIndices(visibleIndices);
    }
  }, [visibleIndices, prevVisibleIndices, testimonials.length]);

  useEffect(() => {
    // Initial check
    checkIfMobile();
    
    // Add debounced resize listener for better performance
    let timeoutId: number;
    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = window.setTimeout(checkIfMobile, 200);
    };
    
    window.addEventListener('resize', handleResize, { passive: true });
    
    // Set initialLoad to false after mount
    setTimeout(() => setInitialLoad(false), 100);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timeoutId);
    };
  }, [checkIfMobile]);

  if (isMobile) {
    return (
      <MobileTestimonialGrid
        visibleIndices={visibleIndices}
        testimonials={testimonials}
        isAnimating={isAnimating}
        direction={direction}
        initialLoad={initialLoad}
        onNext={onNext}
        onPrev={onPrev}
      />
    );
  }

  // Desktop view
  return <DesktopTestimonialGrid testimonials={testimonials} />;
};

export default memo(TestimonialGrid);
