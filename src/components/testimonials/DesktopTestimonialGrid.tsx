
import { motion } from "framer-motion";
import { TestimonialData } from "@/types/testimonial";
import MemoizedTestimonial from "./MemoizedTestimonial";

interface DesktopTestimonialGridProps {
  testimonials: TestimonialData[];
}

const DesktopTestimonialGrid = ({ testimonials }: DesktopTestimonialGridProps) => {
  return (
    <div className="mt-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {testimonials.map((testimonial, index) => (
          <motion.div
            key={`testimonial-grid-${index}`}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-50px" }}
            transition={{ 
              duration: 0.6,
              delay: (index % 3) * 0.12,
              ease: [0.25, 0.1, 0.25, 1]
            }}
            style={{ 
              willChange: "transform, opacity",
              transform: "translateZ(0)",
              perspective: 1000
            }}
            className={`
              ${index % 3 === 1 ? 'md:transform md:translate-y-12' : ''}
              ${index % 12 >= 6 ? 'md:transform md:-translate-y-6' : ''}
            `}
          >
            <MemoizedTestimonial
              data={testimonial} 
              delay={0} 
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default DesktopTestimonialGrid;
