
import { motion } from "framer-motion";
import {
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface FAQProps {
  faqs: {
    question: string;
    answer: string;
  }[];
}

const FAQ = ({ faqs }: FAQProps) => {
  return (
    <section id="faq" className="container mx-auto px-4 py-20 md:py-28 relative">
      {/* Decorative elements */}
      <div className="absolute inset-0 overflow-hidden -z-10">
        <div className="absolute top-20 right-10 w-80 h-80 bg-gold/5 rounded-full blur-[100px] opacity-70"></div>
        <div className="absolute bottom-40 left-10 w-72 h-72 bg-gold/5 rounded-full blur-[100px] opacity-60"></div>
      </div>
      
      <div className="text-center mb-16">
        <motion.h2 
          className="text-2xl sm:text-3xl md:text-5xl font-bold mb-4 enhanced-gradient-text"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          Frequently Asked Questions
        </motion.h2>
        <motion.p 
          className="text-xs sm:text-sm md:text-lg text-gray-300 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
        >
          Everything you need to know about Datawise
        </motion.p>
      </div>
      
      <motion.div 
        className="max-w-3xl mx-auto glass-card p-6 md:p-8 relative overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        viewport={{ once: true }}
        whileHover={{ 
          boxShadow: "0 15px 30px rgba(255, 215, 0, 0.1)"
        }}
      >
        {/* Enhanced glass card with decorative elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-gold/5 to-transparent -z-10"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-gold/10 to-transparent opacity-30 rounded-bl-full"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-gold/10 to-transparent opacity-30 rounded-tr-full"></div>
        
        <Accordion type="single" collapsible className="w-full">
          {faqs.map((faq, index) => (
            <AccordionItem 
              key={index} 
              value={`item-${index}`} 
              className="border-b border-white/10 last:border-0 overflow-hidden"
            >
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.1 + (index * 0.05) }}
                viewport={{ once: true }}
              >
                <AccordionTrigger className="text-left font-semibold py-4 hover:text-gold group text-xs sm:text-sm md:text-base">
                  <div className="flex items-center">
                    <div className="mr-3 opacity-70 text-gold/70 font-mono text-xs">
                      0{index + 1}.
                    </div>
                    <span className="group-hover:translate-x-1 transition-transform duration-300">
                      {faq.question}
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="text-gray-300 pb-4 pl-8 text-xs sm:text-sm leading-relaxed">
                  <div className="bg-gold/5 p-3 sm:p-4 rounded-lg">
                    {faq.answer}
                  </div>
                </AccordionContent>
              </motion.div>
            </AccordionItem>
          ))}
        </Accordion>
        
        {/* Bottom decorative element */}
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-gold/0 via-gold/20 to-gold/0"></div>
      </motion.div>
    </section>
  );
};

export default FAQ;
