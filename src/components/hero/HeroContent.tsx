import { ArrowR<PERSON> } from "lucide-react";
import TestimonialSlider from "./TestimonialSlider";
import { useCheckout } from "../checkout";

interface HeroContentProps {
  currentTestimonialIndex: number;
  setCurrentTestimonialIndex: (index: number) => void;
  scrollToSection: (id: string) => void;
}

export default function HeroContent({
  currentTestimonialIndex,
  setCurrentTestimonialIndex,
  scrollToSection,
}: HeroContentProps) {
  const { openCheckout, preloadCheckout } = useCheckout();
  
  return (
    <div className="w-full mx-0 sm:mx-auto md:mx-0 md:max-w-xl text-center md:text-left space-y-5 md:flex-1">
      <h1
        className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight"
      >
        <span className="heading-gradient">Stop Guessing, Start Winning:</span>{" "}
        Sports Betting{" "}
        <span className="heading-gradient">Backed by Data</span>
      </h1>
      <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-300 leading-relaxed">
        Our tools and community of profitable bettors help turn sports betting
        into a reliable side income.
      </p>
      <div className="flex flex-row gap-3 justify-center md:justify-start">
        <button
          onClick={() => openCheckout()}
          onMouseEnter={preloadCheckout}
          className="btn-primary px-4 py-3 sm:px-5 sm:py-3 rounded-lg text-sm sm:text-base font-medium flex items-center justify-center whitespace-nowrap flex-1 sm:flex-none sm:w-auto relative"
        >
          <span className="absolute inset-0 bg-gold/20 animate-pulse rounded-lg"></span>
          <span className="relative flex items-center">
            <span className="sm:hidden">Try 3 Days Free</span>
            <span className="hidden sm:inline">Start Winning Today - 3 Day Free Trial</span>
            <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
          </span>
        </button>
        <button
          onClick={() => scrollToSection("how-it-works")}
          className="btn-outline px-4 py-3 sm:px-5 sm:py-3 rounded-lg text-sm sm:text-base font-medium whitespace-nowrap flex-1 sm:flex-none sm:w-auto"
        >
          Learn More
        </button>
      </div>
      <TestimonialSlider
        currentTestimonialIndex={currentTestimonialIndex}
        setCurrentTestimonialIndex={setCurrentTestimonialIndex}
      />
    </div>
  );
}