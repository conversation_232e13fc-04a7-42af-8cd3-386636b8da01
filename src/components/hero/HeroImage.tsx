import { useRef } from "react";

const HeroImage = () => {
  const imageRef = useRef<HTMLImageElement>(null);

  // Simplify component - remove state to reduce JS execution
  return (
    <div className="mt-6 sm:mt-8 md:mt-0 w-full max-w-none sm:max-w-5xl md:flex-1 relative mb-4">
      <div
        className="w-full relative rounded-none sm:rounded-xl overflow-hidden aspect-[8/7]"
        style={{
          backgroundColor: "#111111",
          maxHeight: "90vh",
        }}
      >
        <picture>
          {/* PNG format for mobile - relative path for development compatibility */}
          <source
            type="image/png"
            srcSet="/lovable-uploads/HeroImage-400.png 400w"
            media="(max-width: 640px)"
          />
          {/* WebP format for desktop/tablet - relative path for development compatibility */}
          <img
            ref={imageRef}
            src="/lovable-uploads/HeroImage.webp"
            alt="Datawise Dashboard"
            className="w-full h-full object-cover sm:rounded-xl"
            width="1100"
            height="660"
            loading="eager"
            decoding="async"
          />
        </picture>
      </div>
    </div>
  );
};



export default HeroImage;