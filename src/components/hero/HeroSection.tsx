import { useState, useEffect, lazy, Suspense } from "react";
import { testimonials } from "../testimonials/testimonialData";
import HeroContent from "./HeroContent";
import HeroImage from "./HeroImage";
import { useProgressiveRender } from "@/hooks/useProgressiveRender";
import { useCheckout } from "../checkout";

// Lazy load non-critical components
const BackgroundElements = lazy(() => import("./BackgroundElements"));
const CountdownPopup = lazy(() => import("../countdown").then(mod => ({ default: mod.CountdownPopup })));

const HeroSection = () => {
  const [currentTestimonialIndex, setCurrentTestimonialIndex] = useState(0);
  const [showCountdown, setShowCountdown] = useState(false);
  
  // Use progressive render for staged loading
  const stages = useProgressiveRender({
    delays: [0, 100, 3000], // backgroundElements, testimonialSlider, countdownPopup (3 seconds)
    useIdleCallback: true
  });

  // Get checkout state to prevent popup during checkout
  const { isOpen: isCheckoutOpen } = useCheckout();
  
  // Testimonial slider logic
  useEffect(() => {
    if (!stages.stage2) return;
    
    // Simple 5s auto-slide for testimonials.
    const interval = setInterval(() => {
      setCurrentTestimonialIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);
    
    return () => {
      clearInterval(interval);
    };
  }, [stages.stage2]);

  // Countdown popup logic
  useEffect(() => {
    if (!stages.stage3) return;

    // Show popup after delay
    const popupTimer = setTimeout(() => {
      const hasSeenPopup = localStorage.getItem('hasSeenTrialPopup');
      // Don't show popup if user has seen it before OR if checkout is already open
      if (!hasSeenPopup && !isCheckoutOpen) {
        setShowCountdown(true);
      }
    }, 8000);

    return () => {
      clearTimeout(popupTimer);
    };
  }, [stages.stage3, isCheckoutOpen]);

  // Hide popup if checkout opens while popup is visible
  useEffect(() => {
    if (isCheckoutOpen && showCountdown) {
      setShowCountdown(false);
    }
  }, [isCheckoutOpen, showCountdown]);

  // Simple scroll helper
  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleStartTrial = () => {
    localStorage.setItem('hasSeenTrialPopup', 'true');
  };

  const handleCloseCountdown = () => {
    localStorage.setItem('hasSeenTrialPopup', 'true');
    setShowCountdown(false);
  };

  return (
    <section className="relative w-full overflow-hidden px-0 sm:px-4 pt-24 pb-12 md:pt-36 md:pb-16">
      {/* Decorative background - lazy loaded */}
      {stages.stage1 && (
        <Suspense fallback={null}>
          <BackgroundElements />
        </Suspense>
      )}

      <div className="container mx-auto px-4 sm:px-0">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between md:gap-12">
          {/* The text content */}
          <HeroContent 
            currentTestimonialIndex={currentTestimonialIndex}
            setCurrentTestimonialIndex={setCurrentTestimonialIndex}
            scrollToSection={scrollToSection}
          />
          {/* Hero image - highest priority */}
          <HeroImage />
        </div>
      </div>

      {/* Delayed popup - only show if checkout is not open */}
      {showCountdown && stages.stage3 && !isCheckoutOpen && (
        <Suspense fallback={null}>
          <CountdownPopup
            initialTimeInHours={0.25}
            onClose={handleCloseCountdown}
            onStartTrial={handleStartTrial}
          />
        </Suspense>
      )}
    </section>
  );
};

export default HeroSection;