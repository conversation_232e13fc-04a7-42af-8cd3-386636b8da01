import React, { useEffect, useState, useTransition, useLayoutEffect } from 'react';
import { WhopCheckoutEmbed } from '@whop/react/checkout';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Shield, Lock, CreditCard, X } from "lucide-react";
import { Analytics } from '@/utils/analytics';
import { Button } from '@/components/ui/button';

interface CheckoutModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  planId?: string;
  theme?: 'light' | 'dark' | 'system';
}

export const CheckoutModal: React.FC<CheckoutModalProps> = ({
  isOpen,
  onOpenChange,
  planId = "plan_NCr6hVh2qtYBb",
  theme = 'dark',
}) => {
  const [showFallback, setShowFallback] = useState(false);
  const [, startTransition] = useTransition();
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile viewport
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Track when modal opens
  useEffect(() => {
    if (isOpen) {
      Analytics.trackEvent('checkout_modal_opened', {
        plan_id: planId,
        theme: theme,
        device: isMobile ? 'mobile' : 'desktop',
      });
    }
  }, [isOpen, planId, theme, isMobile]);

  // Lock body scroll when mobile modal is open
  useLayoutEffect(() => {
    if (isOpen && isMobile) {
      document.body.classList.add('dwb-no-scroll');
    } else {
      document.body.classList.remove('dwb-no-scroll');
    }

    return () => {
      document.body.classList.remove('dwb-no-scroll');
    };
  }, [isOpen, isMobile]);

  // Listen for checkout completion messages
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== 'https://whop.com') return;

      if (event.data.type === 'checkout-complete' ||
          event.data.action === 'checkout.completed') {
        Analytics.trackEvent('checkout_completed', {
          plan_id: planId,
        });
        onOpenChange(false);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [planId, onOpenChange]);

  const handleOpenChange = (open: boolean) => {
    startTransition(() => {
      if (!open) {
        Analytics.trackEvent('checkout_modal_closed', {
          plan_id: planId,
        });
      }
      onOpenChange(open);
    });
  };

  const handleFallbackClick = () => {
    Analytics.trackEvent('checkout_fallback_clicked', {
      plan_id: planId,
    });
    window.open(`https://whop.com/checkout/${planId}/`, '_blank');
  };

  // Mobile-optimized modal checkout (not full-screen)
  if (isMobile && isOpen) {
    return (
      <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm">
        {/* Mobile modal container */}
        <div className="fixed inset-x-4 top-8 bottom-8 bg-black border border-gold/20 rounded-lg shadow-2xl flex flex-col overflow-hidden">
          {/* Mobile header */}
          <div className="flex items-center justify-between p-4 border-b border-white/10 shrink-0">
            <h2 className="text-lg font-semibold text-white">3-Day Free Trial</h2>
            <button
              onClick={() => handleOpenChange(false)}
              className="p-1 text-gray-400 hover:text-white transition-colors"
              aria-label="Close checkout"
            >
              <X size={20} />
            </button>
          </div>

          {/* Scrollable checkout content */}
          <div className="flex-1 overflow-y-auto -webkit-overflow-scrolling-touch">
            <style dangerouslySetInnerHTML={{ __html: `
              /* Mobile-specific Whop iframe styles - let it size naturally */
              @media (max-width: 640px) {
                .whop-checkout-embed {
                  width: 100% !important;
                  height: fit-content !important;
                  min-height: 500px !important;
                  max-height: none !important;
                }
                .whop-checkout-embed iframe,
                [data-whop-checkout-container] iframe {
                  width: 100% !important;
                  height: fit-content !important;
                  min-height: 500px !important;
                  max-height: none !important;
                  border: none !important;
                }
              }
            `}} />

            {!showFallback ? (
              <div className="p-4">
                <WhopCheckoutEmbed
                  planId={planId}
                  theme={theme}
                  fallback={
                    <div className="flex items-center justify-center py-20">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold mx-auto mb-3"></div>
                        <p className="text-gray-400 text-sm">Loading checkout...</p>
                      </div>
                    </div>
                  }
                />

                {/* Fallback link */}
                <div className="text-center py-4">
                  <button
                    onClick={() => setShowFallback(true)}
                    className="text-xs text-gray-500 underline"
                  >
                    Issues? Try external checkout
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-6 min-h-[300px]">
                <p className="text-gray-300 mb-4 text-sm text-center">
                  Open checkout in a new window:
                </p>
                <Button
                  onClick={handleFallbackClick}
                  className="bg-gold hover:bg-gold/90 text-black font-medium px-6 py-2 text-sm"
                >
                  Open Checkout
                </Button>
                <button
                  onClick={() => setShowFallback(false)}
                  className="mt-4 text-xs text-gray-400 underline"
                >
                  Back to embedded checkout
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Desktop/tablet experience (unchanged)
  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[700px] md:max-w-[800px] lg:max-w-[900px] sm:max-h-[95vh] p-0 bg-black/95 border border-gold/20 backdrop-blur-xl flex flex-col overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gold/10 via-transparent to-gold/5 pointer-events-none rounded-lg" />
        
        <DialogHeader className="relative z-10 p-6 pb-4 border-b border-white/10 shrink-0">
          <DialogTitle className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Start Your 3-Day Free Trial
          </DialogTitle>
          <DialogDescription className="text-base text-gray-400 mt-2">
            Join thousands of profitable bettors. Cancel anytime during your trial.
          </DialogDescription>
          
          {import.meta.env.DEV && (
            <div className="mt-3 p-2 bg-yellow-500/10 border border-yellow-500/20 rounded text-xs text-yellow-500/80">
              <strong>Dev Note:</strong> Embedded checkout is blocked on localhost for security. This works on production with HTTPS. Use the fallback button below to test checkout flow.
            </div>
          )}
          
          <div className="flex flex-wrap gap-4 mt-4 text-sm">
            <div className="flex items-center gap-1.5">
              <svg className="w-4 h-4 text-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-gray-300">Instant access</span>
            </div>
            <div className="flex items-center gap-1.5">
              <Shield className="w-4 h-4 text-gold" />
              <span className="text-gray-300">Secure checkout</span>
            </div>
            <div className="flex items-center gap-1.5">
              <Lock className="w-4 h-4 text-gold" />
              <span className="text-gray-300">SSL encrypted</span>
            </div>
            <div className="flex items-center gap-1.5">
              <CreditCard className="w-4 h-4 text-gold" />
              <span className="text-gray-300">Major cards accepted</span>
            </div>
          </div>
        </DialogHeader>
        
        <div className="relative z-10 flex-1 w-full bg-black/50 p-6 overflow-auto" style={{ minHeight: '700px' }}>
          {!showFallback ? (
            <div className="h-full relative">
              <WhopCheckoutEmbed
                planId={planId}
                theme={theme}
                fallback={
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold mx-auto mb-4"></div>
                      <p className="text-gray-400">Loading secure checkout...</p>
                    </div>
                  </div>
                }
              />
              
              {/* Fallback button for development or if embed fails */}
              <div className="mt-4 text-center">
                <button
                  onClick={() => setShowFallback(true)}
                  className="text-sm text-gray-400 hover:text-white underline transition-colors"
                >
                  Having issues? Use direct checkout
                </button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full">
              <p className="text-gray-300 mb-6">
                Click below to open checkout in a new window:
              </p>
              <Button
                onClick={handleFallbackClick}
                className="bg-gold hover:bg-gold/90 text-black font-medium px-8 py-3"
              >
                Open Checkout
              </Button>
              <button
                onClick={() => setShowFallback(false)}
                className="mt-4 text-sm text-gray-400 hover:text-white underline transition-colors"
              >
                Try embedded checkout again
              </button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};