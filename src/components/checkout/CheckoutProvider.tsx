import React, { createContext, useContext, useState, useTransition } from 'react';
import { Analytics } from '@/utils/analytics';
import { CheckoutModal } from './CheckoutModal';

interface CheckoutContextType {
  openCheckout: (planId?: string) => void;
  closeCheckout: () => void;
  isOpen: boolean;
  preloadCheckout: () => void;
}

const CheckoutContext = createContext<CheckoutContextType | undefined>(undefined);

export const useCheckout = () => {
  const context = useContext(CheckoutContext);
  if (!context) {
    // Return a no-op implementation when used outside of provider
    // This can happen during lazy loading or SSR
    console.warn('useCheckout used outside of CheckoutProvider');
    return {
      openCheckout: () => console.warn('Checkout not available - component may be rendering outside CheckoutProvider'),
      closeCheckout: () => {},
      isOpen: false,
      preloadCheckout: () => {}
    };
  }
  return context;
};

interface CheckoutProviderProps {
  children: React.ReactNode;
  defaultPlanId?: string;
}

// Plan IDs mapping
const PLAN_IDS = {
  exclusive: "plan_NCr6hVh2qtYBb", // 7 day exclusive
  standard: "plan_r7ByM1xdFuSmK"   // 3 day normal
};

export const CheckoutProvider: React.FC<CheckoutProviderProps> = ({ 
  children,
  defaultPlanId = PLAN_IDS.standard // Default to 3-day trial
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [planId, setPlanId] = useState(defaultPlanId);
  
  // React 19 useTransition for non-urgent state updates
  const [isPending, startTransition] = useTransition();

  const preloadCheckout = () => {
    // Preload the Whop checkout script
    const script = document.createElement('script');
    script.src = 'https://js.whop.com/static/checkout/loader.js';
    script.async = true;
    script.defer = true;
    
    // Only append if not already loaded
    if (!document.querySelector('script[src*="js.whop.com/static/checkout/loader.js"]')) {
      document.head.appendChild(script);
    }
  };

  const openCheckout = (planType?: string) => {
    // Determine which plan to use
    let selectedPlanId = defaultPlanId;
    
    if (planType === 'exclusive') {
      selectedPlanId = PLAN_IDS.exclusive;
    } else if (planType === 'standard') {
      selectedPlanId = PLAN_IDS.standard;
    } else if (planType && typeof planType === 'string' && planType.startsWith('plan_')) {
      // Direct plan ID passed
      selectedPlanId = planType;
    }
    
    // Track checkout opened immediately
    Analytics.trackEvent('checkout_modal_opened', {
      plan_id: selectedPlanId,
      method: 'modal_embed'
    });
    
    // Use transition for non-urgent state updates
    startTransition(() => {
      setPlanId(selectedPlanId);
      setIsOpen(true);
    });
  };

  const closeCheckout = () => {
    startTransition(() => {
      setIsOpen(false);
    });
  };

  return (
    <CheckoutContext.Provider value={{ 
      openCheckout, 
      closeCheckout, 
      isOpen, 
      preloadCheckout 
    }}>
      {children}
      <CheckoutModal
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        planId={planId}
        theme="dark"
      />
    </CheckoutContext.Provider>
  );
};