
import { memo } from "react";
import { motion } from "framer-motion";

interface CountdownButtonProps {
  onClick: () => void;
  children: React.ReactNode;
}

// Memoize the button to prevent unnecessary rerenders
export const CountdownButton = memo(({ onClick, children }: CountdownButtonProps) => {
  return (
    <motion.button
      onClick={onClick}
      className="w-full py-2 sm:py-2.5 bg-gold hover:bg-gold/90 text-black text-sm sm:text-base font-medium rounded-lg text-center transition-colors btn-interactive relative overflow-hidden group"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ 
        type: "spring", 
        stiffness: 400, 
        damping: 17 
      }}
    >
      <motion.span 
        className="absolute inset-0 bg-white/20 rounded-lg"
        animate={{ opacity: [0.2, 0.4, 0.2] }}
        transition={{ 
          repeat: Infinity,
          duration: 2,
          ease: "easeInOut"
        }}
      />
      <span className="relative">{children}</span>
    </motion.button>
  );
});

// Add display name for React DevTools
CountdownButton.displayName = "CountdownButton";
