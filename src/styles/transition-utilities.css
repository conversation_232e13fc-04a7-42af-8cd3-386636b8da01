
/* Smooth animation preset utility classes - optimized for performance */

.smooth-fade {
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity;
}

.smooth-transform {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

/* Modified for better performance - removed will-change for non-transform/opacity properties */
.smooth-all {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* New utility classes for common animation patterns */
.smooth-scale {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.smooth-rotate {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

/* Optimized properties for elements that change position */
.smooth-position {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

/* Performance hint for hover effects */
.hover-hint {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Enhanced class for testimonial transitions - with reduced layout shifts */
.testimonial-transition {
  transition: opacity 1s cubic-bezier(0.25, 0.1, 0.25, 1), transform 1s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: opacity, transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000;
  contain: paint style;
}

/* Carousel container - prevent layout shifts */
.carousel-container {
  position: relative;
  overflow: hidden;
  contain: layout size style;
}

.carousel-slide {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  opacity: 0;
  pointer-events: none;
  transition: opacity 1s ease;
  will-change: opacity;
  transform: translateZ(0);
}

.carousel-slide.active {
  opacity: 1;
  pointer-events: auto;
}

/* Enhanced swipeable carousel classes */
.swipeable-container {
  touch-action: pan-y;
  user-select: none;
  contain: content;
}

/* Smooth infinite carousel animation */
.infinite-carousel {
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-fill-mode: forwards;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  contain: paint style;
}

@keyframes smoothScroll {
  from { transform: translate3d(0, 0, 0); }
  to { transform: translate3d(-100%, 0, 0); }
}

/* Mobile-specific transitions - smoother and longer duration */
@media (max-width: 767px) {
  .testimonial-transition {
    transition: opacity 1.2s cubic-bezier(0.25, 0.1, 0.25, 1), transform 1.2s cubic-bezier(0.25, 0.1, 0.25, 1);
    contain: content;
  }
  
  .smooth-fade,
  .smooth-transform,
  .smooth-scale,
  .smooth-rotate,
  .smooth-position {
    transition-duration: 0.75s;
    transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  }
}

/* Reduced motion preference handling */
@media (prefers-reduced-motion: reduce) {
  .smooth-fade,
  .smooth-transform,
  .smooth-all,
  .smooth-scale,
  .smooth-rotate,
  .smooth-position,
  .testimonial-transition {
    transition: none !important;
    will-change: auto !important;
  }
}
