
/* Enhanced interactive button effects */
.btn-interactive {
  position: relative;
  overflow: hidden;
  isolation: isolate;
  transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.btn-interactive::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255,215,0,0.15), rgba(255,215,0,0));
  transform: translateX(-100%) rotate(45deg);
  transition: transform 0.6s cubic-bezier(0.2, 0.8, 0.2, 1);
  z-index: -1;
}

.btn-interactive:hover::before {
  transform: translateX(100%) rotate(45deg);
}

.btn-interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 30px rgba(255, 215, 0, 0.2);
}
