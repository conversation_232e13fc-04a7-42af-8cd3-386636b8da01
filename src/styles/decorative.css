
/* Enhanced decorative dot pattern */
.dot-pattern {
  position: relative;
  z-index: 0;
}

.dot-pattern::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background-image: radial-gradient(rgba(255, 215, 0, 0.2) 2px, transparent 2px);
  background-size: 20px 20px;
  opacity: 0.4;
  z-index: -1;
  transform: rotate(15deg);
  animation: pulse-subtle 6s infinite alternate;
}

/* Custom radial gradient for corner accents */
.bg-gradient-radial {
  background-image: radial-gradient(circle, var(--tw-gradient-from) 0%, var(--tw-gradient-via) 40%, var(--tw-gradient-to) 100%);
}

/* Gold radial gradient */
.radial-gold-gradient {
  background: radial-gradient(circle, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.05) 50%, transparent 70%);
}

/* Enhanced accent decorations */
.corner-accent {
  position: relative;
  overflow: hidden;
}

.corner-accent::before {
  content: '';
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.05) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.corner-accent:hover::before {
  opacity: 1;
}
