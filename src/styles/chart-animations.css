
/* Chart and visualization animations optimized for performance */

.chart-animation-enter {
  opacity: 0;
  transform: translateY(10px);
  will-change: opacity, transform;
}

.chart-animation-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 400ms, transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-bar-reveal {
  transform-origin: bottom;
  animation: bar-reveal 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  will-change: transform;
}

@keyframes bar-reveal {
  from {
    transform: scaleY(0);
  }
  to {
    transform: scaleY(1);
  }
}

/* Line chart animation - optimized for smoother renders */
.line-reveal {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: line-reveal 2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  will-change: stroke-dashoffset;
}

@keyframes line-reveal {
  to {
    stroke-dashoffset: 0;
  }
}

/* Pie chart animation */
.pie-reveal {
  transform-origin: center;
  animation: pie-reveal 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  will-change: transform, opacity;
}

@keyframes pie-reveal {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .chart-animation-enter-active,
  .chart-bar-reveal,
  .line-reveal,
  .pie-reveal {
    animation: none !important;
    transition: none !important;
    transform: none !important;
    opacity: 1 !important;
    stroke-dashoffset: 0 !important;
  }
}
