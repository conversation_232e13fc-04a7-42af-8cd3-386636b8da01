
/* Decorative and particle animations */

/* New particle effect with smoother transition */
.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: rgba(255, 215, 0, 0.3);
  pointer-events: none;
  animation: rise 10s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  will-change: transform, opacity;
}

@keyframes rise {
  0% {
    opacity: 0;
    transform: translateY(100%) translateX(0);
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: translateY(-100%) translateX(50px);
  }
}

/* Enhanced text gradient with smoother transition */
.enhanced-gradient-text {
  background: linear-gradient(135deg, #FFD700 0%, #FFC107 50%, #FFD700 100%);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shine 4s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  letter-spacing: -0.02em;
}

@keyframes shine {
  to {
    background-position: 200% center;
  }
}

@keyframes pulse-subtle {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.5;
  }
}
