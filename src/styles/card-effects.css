
/* Enhanced parallax hover effect */
.parallax-card {
  transition: transform 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
  transform-style: preserve-3d;
  perspective: 1000px;
  position: relative;
  overflow: hidden;
}

.parallax-card:hover {
  transform: rotateX(5deg) rotateY(5deg) scale(1.02);
}

.parallax-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.05) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
  border-radius: inherit;
  z-index: -1;
}

.parallax-card:hover::before {
  opacity: 1;
}

/* New 3D card effect */
.card-3d {
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.2, 0.8, 0.2, 1);
  overflow: hidden;
}

.card-3d:hover {
  transform: translateZ(20px);
}

.card-3d::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.05) 50%, transparent 70%);
  transform: translateZ(-20px);
  transition: opacity 0.6s cubic-bezier(0.2, 0.8, 0.2, 1);
  opacity: 0;
  border-radius: inherit;
  z-index: -1;
}

.card-3d:hover::before {
  opacity: 1;
}

/* Enhanced border glow */
.border-glow {
  border: 1px solid rgba(255, 215, 0, 0.1);
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.1);
  transition: border 0.3s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
}

.border-glow:hover {
  border: 1px solid rgba(255, 215, 0, 0.2);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
}

.border-glow::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.05) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
  border-radius: inherit;
  z-index: -1;
}

.border-glow:hover::before {
  opacity: 1;
}
