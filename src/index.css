/* Google Fonts are now loaded via preload in index.html */

/* Import accessibility styles first */
@import './styles/accessibility.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 7%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3%;
    --card-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --primary: 44 92% 63%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --accent: 44 92% 63%;
    --accent-foreground: 0 0% 9%;
    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  html, body, #root {
    @apply bg-black min-h-screen;
  }

  body {
    @apply text-foreground font-inter antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
    background: radial-gradient(ellipse at top, rgba(20, 20, 20, 1) 0%, rgba(10, 10, 10, 1) 100%) fixed;
  }
}

@layer components {
  .glass-card {
    @apply bg-black/30 backdrop-blur-xl border border-white/10 rounded-xl relative overflow-hidden shadow-[0_10px_30px_rgba(0,0,0,0.2)];
  }

  .btn-primary {
    @apply bg-gold hover:bg-gold-light text-black font-semibold px-7 py-3.5 rounded-lg transition-all duration-300 ease-out shadow-[0_5px_25px_rgba(247,197,72,0.3)] relative overflow-hidden;
  }

  .btn-primary::before {
    @apply content-[''] absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300;
  }

  .btn-primary:hover::before {
    @apply opacity-100;
  }

  .btn-outline {
    @apply border-2 border-gold/30 hover:border-gold/70 text-white font-semibold px-7 py-3.5 rounded-lg transition-all duration-300 ease-out shadow-[0_5px_25px_rgba(247,197,72,0.15)] relative overflow-hidden;
  }

  .btn-outline::before {
    @apply content-[''] absolute inset-0 bg-gradient-to-r from-gold/5 to-transparent opacity-0 transition-opacity duration-300;
  }

  .btn-outline:hover::before {
    @apply opacity-100;
  }

  .nav-link {
    @apply text-gray-300 hover:text-white transition-colors duration-300 relative;
    text-decoration: none;
  }

  .nav-link::after {
    @apply content-[''] absolute left-0 right-0 bottom-2 h-0.5 bg-gold/50 transform scale-x-0 origin-left transition-transform duration-300;
  }

  .nav-link:hover::after {
    @apply transform scale-x-100;
  }

  .heading-gradient {
    @apply bg-gradient-to-r from-gold via-gold-light to-gold bg-clip-text text-transparent;
  }

}

.fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover animations for cards */
.glass-card {
  transition: box-shadow 0.4s ease, border-color 0.4s ease;
}

.glass-card:hover {
  box-shadow: 0 0 40px rgba(247, 197, 72, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Feature card animation */
.feature-card {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  z-index: -1;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(247, 197, 72, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.8s ease;
}

.feature-card:hover::before {
  opacity: 1;
}

/* Step number animation */
.step-number {
  position: relative;
}

.step-number::after {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border: 1px solid rgba(247, 197, 72, 0.4);
  border-radius: 50%;
  animation: pulse 4s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

/* Text shimmer effect */
.text-shimmer {
  background: linear-gradient(90deg, rgba(247,197,72,1) 0%, rgba(255,229,132,0.9) 50%, rgba(247,197,72,1) 100%);
  background-size: 200% auto;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 4s linear infinite;
}

@keyframes shimmer {
  to {
    background-position: 200% center;
  }
}

/* Subtle background animation */
.bg-animate {
  background: linear-gradient(45deg, rgba(0,0,0,0.7) 0%, rgba(30,30,30,0.7) 50%, rgba(0,0,0,0.7) 100%);
  background-size: 200% 200%;
  animation: gradientBG 15s ease infinite;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Scrollbar customization */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgba(247, 197, 72, 0.25);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(247, 197, 72, 0.35);
}

/* Selection styling */
::selection {
  background: rgba(247, 197, 72, 0.25);
  color: white;
}

/* Body scroll lock utility class */
.dwb-no-scroll {
  overflow: hidden;
}

/* Utilities layer - highest priority overrides */
@layer utilities {
  /* Testimonial navigation dots with proper specificity */
  button.testimonial-nav-dot {
    width: 4px;
    height: 4px;
    min-width: 4px;
    min-height: 4px;
    max-width: 4px;
    max-height: 4px;
    font-size: 0;
    flex-shrink: 0;
    box-sizing: border-box;
  }

  @media (min-width: 640px) {
    button.testimonial-nav-dot {
      width: 6px;
      height: 6px;
      min-width: 6px;
      min-height: 6px;
      max-width: 6px;
      max-height: 6px;
    }
  }

  /* Testimonial pagination dots with proper specificity */
  .testimonial-pagination-dot {
    height: 8px;
    min-height: 8px;
    max-height: 8px;
    font-size: 0;
    flex-shrink: 0;
    box-sizing: border-box;
  }

  /* Simple testimonial dots with proper specificity */
  .simple-testimonial-dot {
    width: 8px;
    height: 8px;
    min-width: 8px;
    min-height: 8px;
    max-width: 8px;
    max-height: 8px;
    font-size: 0;
    flex-shrink: 0;
    box-sizing: border-box;
  }
}
