import { useState, useEffect } from 'react';

interface ProgressiveRenderOptions {
  delays: number[];
  useIdleCallback?: boolean;
}

interface ProgressiveRenderStages {
  [key: string]: boolean;
}

/**
 * Custom hook for progressive rendering of components
 * Manages staged loading of content to improve perceived performance
 * 
 * @param options - Configuration object with delays array and optional useIdleCallback flag
 * @returns Object with boolean flags for each render stage
 * 
 * @example
 * const stages = useProgressiveRender({
 *   delays: [0, 100, 500], // Immediate, 100ms, 500ms
 *   useIdleCallback: true
 * });
 * 
 * return (
 *   <>
 *     {stages.stage1 && <CriticalContent />}
 *     {stages.stage2 && <SecondaryContent />}
 *     {stages.stage3 && <DeferredContent />}
 *   </>
 * );
 */
export function useProgressiveRender({ 
  delays, 
  useIdleCallback = true 
}: ProgressiveRenderOptions): ProgressiveRenderStages {
  const initialStages = delays.reduce((acc, _, index) => {
    acc[`stage${index + 1}`] = false;
    return acc;
  }, {} as ProgressiveRenderStages);

  const [stages, setStages] = useState<ProgressiveRenderStages>(initialStages);

  useEffect(() => {
    const timers: (number | NodeJS.Timeout)[] = [];

    delays.forEach((delay, index) => {
      const stageKey = `stage${index + 1}`;
      
      if (delay === 0) {
        // Immediate render
        setStages(prev => ({ ...prev, [stageKey]: true }));
      } else if (useIdleCallback && 'requestIdleCallback' in window) {
        // Use requestIdleCallback for better performance
        const handle = requestIdleCallback(
          () => {
            setStages(prev => ({ ...prev, [stageKey]: true }));
          },
          { timeout: delay }
        );
        timers.push(handle);
      } else {
        // Fallback to setTimeout
        const timer = setTimeout(() => {
          setStages(prev => ({ ...prev, [stageKey]: true }));
        }, delay);
        timers.push(timer);
      }
    });

    // Cleanup function
    return () => {
      timers.forEach(timer => {
        if (typeof timer === 'number' && 'cancelIdleCallback' in window) {
          cancelIdleCallback(timer);
        } else {
          clearTimeout(timer as NodeJS.Timeout);
        }
      });
    };
  }, []); // Run only once on mount

  return stages;
}

/**
 * Preset configurations for common use cases
 */
export const PROGRESSIVE_RENDER_PRESETS = {
  standard: {
    delays: [0, 100, 500],
    useIdleCallback: true
  },
  aggressive: {
    delays: [0, 50, 200],
    useIdleCallback: true
  },
  conservative: {
    delays: [0, 200, 1000],
    useIdleCallback: true
  }
};